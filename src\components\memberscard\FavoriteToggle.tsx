import { useToggleFavorite } from "@/api";
import { Heart } from "iconsax-react";
import { useEffect, useState } from "react";
import { Spinner } from "react-bootstrap";

interface FavoriteToggleProps {
  modelId: number;
  initialFavorite: boolean;
  refetchModels?: () => void;
}

const FavoriteToggle: React.FC<FavoriteToggleProps> = ({
  modelId,
  initialFavorite,
  refetchModels,
}) => {
  const [isFavorite, setIsFavorite] = useState(initialFavorite);
  const { mutateAsync: toggleFavorite, isPending } = useToggleFavorite();

  const handleToggle = async (e: any) => {
    e.stopPropagation();
    try {
      const res: any = await toggleFavorite({ modelId });
      if (res?.success) {
        setIsFavorite(!isFavorite);
        refetchModels?.();
      }
    } catch (err) {
      console.log(err);
    }
  };

  useEffect(() => {
    setIsFavorite(initialFavorite);
  }, [initialFavorite]);

  return (
    <div
      className="favorite-icon"
      style={{ cursor: "pointer" }}
      onClick={handleToggle}
    >
      {isPending ? (
        <Spinner animation="border" size="sm" />
      ) : (
        <Heart
          size="24"
          color={isFavorite ? "#ff0000" : "#999"}
          variant={isFavorite ? "Bold" : "Outline"}
        />
      )}
    </div>
  );
};

export default FavoriteToggle;
