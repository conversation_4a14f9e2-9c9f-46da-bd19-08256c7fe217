import { useDeleteFlirtMutation, useInfiniteFlirts } from "@/api";
import { InfiniteList } from "@/components/common/InfiniteList";
import FlirtCard, { FlirtCardSkeleton } from "@/components/flirtcard/FlirtCard";
import { useTranslation } from "@/hooks/useTranslation";
import { ROUTE_PATH } from "@/routes";
import { setConfirmModalConfig } from "@/stores";
import { FlirtInterface } from "@/types";
import React, { useMemo, useState } from "react";
import { Al<PERSON>, Button, Container } from "react-bootstrap";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import "./styles.scss";

const Flirts: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [flirtType, setFlirtType] = useState<"received" | "sent">("received");

  const {
    data,
    isLoading,
    isError,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = useInfiniteFlirts({
    type: flirtType,
    params: { limit: 10 },
  });

  const { mutateAsync: deleteFlirt } = useDeleteFlirtMutation();

  const flirts = useMemo(() => {
    return data?.pages?.flatMap((page) => page?.data?.flirts || []) || [];
  }, [data]);

  const handleDeleteFlirt = (flirtId: number) => {
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: async () => {
          try {
            await deleteFlirt(flirtId);
            toast.success("Flirt deleted successfully!");
          } catch (error: any) {
            toast.error(error?.message || "Failed to delete flirt");
          }
        },
        content: {
          heading: "Delete Flirt",
          description: "Are you sure you want to delete this flirt?",
        },
        showCloseIcon: true,
        buttonText: "Delete",
        icon: "Trash",
        iconColor: "#dc3545",
      },
    });
  };

  const handleGoHome = () => {
    navigate(ROUTE_PATH.HOME);
  };

  if (isError && !isLoading) {
    return (
      <div className="flirts-page">
        <Container fluid>
          <div className="d-flex flex-column gap-3">
            <div className="d-flex align-items-center gap-3">
              <Button
                className={
                  flirtType === "received" ? "second-btn active" : "second-btn"
                }
                onClick={() => setFlirtType("received")}
              >
                {t("flirts.received")}
              </Button>
              <Button
                className={
                  flirtType === "sent" ? "first-btn active" : "first-btn"
                }
                onClick={() => setFlirtType("sent")}
              >
                {t("flirts.sent")}
              </Button>
            </div>

            <Alert variant="warning" className="text-center">
              <h5>No flirts found</h5>
              <p>You don't have any {flirtType} flirts yet.</p>
              <Button variant="primary" onClick={handleGoHome}>
                Go to Home
              </Button>
            </Alert>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div className="flirts-page">
      <Container fluid>
        <div className="d-flex flex-column gap-3">
          <div className="d-flex align-items-center gap-3">
            <Button
              className={
                flirtType === "received" ? "second-btn active" : "second-btn"
              }
              onClick={() => setFlirtType("received")}
            >
              {t("flirts.received")}
            </Button>
            <Button
              className={
                flirtType === "sent" ? "first-btn active" : "first-btn"
              }
              onClick={() => setFlirtType("sent")}
            >
              {t("flirts.sent")}
            </Button>
          </div>

          <InfiniteList<FlirtInterface>
            items={flirts}
            hasNextPage={hasNextPage}
            isLoading={isLoading}
            isFetchingNextPage={isFetchingNextPage}
            fetchNextPage={fetchNextPage}
            renderItem={(flirt) => (
              <FlirtCard
                key={flirt.id}
                {...flirt}
                onDelete={handleDeleteFlirt}
              />
            )}
            renderSkeleton={() => <FlirtCardSkeleton />}
            skeletonCount={5}
            emptyState={
              <Alert variant="" className="text-center">
                <h5>No {flirtType} flirts</h5>
                <p>You don't have any {flirtType} flirts yet.</p>
                <Button variant="primary" onClick={handleGoHome}>
                  Go to Home
                </Button>
              </Alert>
            }
            className="flirts-list"
            containerClassName="flirts-container"
          />
        </div>
      </Container>
    </div>
  );
};

export default Flirts;
