import React, { memo, ReactNode } from 'react';
import { Spinner } from 'react-bootstrap';
import { useInfiniteScroll } from '@/hooks';

interface InfiniteListProps<T> {
  items: T[];
  hasNextPage: boolean;
  isLoading: boolean;
  isFetchingNextPage: boolean;
  fetchNextPage: () => void;
  renderItem: (item: T, index: number) => ReactNode;
  renderSkeleton: () => ReactNode;
  skeletonCount?: number;
  emptyState?: ReactNode;
  className?: string;
  containerClassName?: string;
}

const InfiniteList = <T,>({
  items,
  hasNextPage,
  isLoading,
  isFetchingNextPage,
  fetchNextPage,
  renderItem,
  renderSkeleton,
  skeletonCount = 10,
  emptyState,
  className = '',
  containerClassName = '',
}: InfiniteListProps<T>) => {
  const { loadMoreRef } = useInfiniteScroll({
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    rootMargin: '200px',
  });

  // Show skeleton grid for initial loading
  if (isLoading && items.length === 0) {
    return (
      <div className={containerClassName}>
        <div className={className}>
          {Array.from({ length: skeletonCount }).map((_, index) => (
            <React.Fragment key={`skeleton-${index}`}>
              {renderSkeleton()}
            </React.Fragment>
          ))}
        </div>
      </div>
    );
  }

  // Show empty state if no items and not loading
  if (!isLoading && items.length === 0) {
    return emptyState ? <>{emptyState}</> : null;
  }

  return (
    <div className={containerClassName}>
      <div className={className}>
        {items.map((item, index) => (
          <React.Fragment key={index}>
            {renderItem(item, index)}
          </React.Fragment>
        ))}
      </div>
      
      {/* Infinite scroll trigger element */}
      <div ref={loadMoreRef} style={{ height: '20px' }} />
      
      {/* Loading more indicator */}
      {isFetchingNextPage && (
        <div className="d-flex justify-content-center align-items-center w-100 py-4">
          <Spinner animation="border" />
        </div>
      )}
    </div>
  );
};

// Memoize the component for better performance
const MemoizedInfiniteList = memo(InfiniteList) as typeof InfiniteList;
MemoizedInfiniteList.displayName = 'InfiniteList';

export default MemoizedInfiniteList;
