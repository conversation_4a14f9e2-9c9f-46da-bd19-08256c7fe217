@use '@/variables' as *;

.flirts-page {
    padding: 24px 0px;
    width: 100%;
    position: relative;

    .first-btn {
        padding: 15px 40px;
        font-size: 14px;
        min-width: auto;
        border-radius: 8px;
        border: 1px solid $light-gray-color;
        background: $light-yellow;
        color: $black-color;
        transition: all 0.3s ease;

        &:hover, &.active {
            background: linear-gradient(270deg, #B936AD 0%, #79259C 100%);
            color: $white-color;
            border-color: transparent;
        }
    }

    .second-btn {
        border-radius: 8px;
        border: 1px solid $light-gray-color;
        background: $white-color;
        padding: 15px 40px;
        color: $black-color;
        font-size: 14px;
        min-width: auto;
        transition: all 0.3s ease;

        &:hover, &.active {
            background: #79259C;
            color: $white-color;
            border-color: transparent;
        }
    }

    .flirts-container {
        margin-top: 20px;
    }

    .flirts-list {
        display: flex;
        flex-direction: column;
        gap: 0;
    }
}

// Responsive adjustments
@media (max-width: 576px) {
    .flirts-page {
        padding: 16px 0px;

        .first-btn,
        .second-btn {
            padding: 12px 24px;
            font-size: 13px;
        }
    }
}