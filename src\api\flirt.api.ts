import { API_ENDPOINTS } from "@/globals";
import { useInfiniteQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "./apiClient";

export const useInfiniteFlirts = ({ type = "sent", params = {} }) =>
  useInfiniteQuery({
    queryFn: async ({ pageParam = 1 }) => {
      const url = API_ENDPOINTS.GET_SENT_OR_RECEIVED_FLIRTS(type);

      const response = await apiClient.get(url, {
        params: { ...params, page: pageParam },
      });
      return response;
    },
    queryKey: [`infinite-${type}-flirts`, ...Object.values(params)],
    getNextPageParam: (lastPage) => {
      const { meta } = lastPage?.data || {};
      const currentPage = parseInt(meta?.page || "1");
      const totalPages = meta?.pages || 1;

      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    initialPageParam: 1,
  });

export const useSendFlirtMutation = () => {
  return useMutation({
    mutationFn: (payload: Record<string, any>) => {
      return apiClient.post(API_ENDPOINTS.SEND_FLIRT, payload);
    },
  });
};

export const useDeleteFlirtMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (flirtId: number) => {
      return apiClient.delete(API_ENDPOINTS.DELETE_FLIRT(flirtId));
    },
    onSuccess: () => {
      // Invalidate both sent and received flirts queries
      queryClient.invalidateQueries({ queryKey: ["infinite-sent-flirts"] });
      queryClient.invalidateQueries({ queryKey: ["infinite-received-flirts"] });
      queryClient.invalidateQueries({ queryKey: ["sent-flirts"] });
      queryClient.invalidateQueries({ queryKey: ["received-flirts"] });
    },
  });
};
