import { API_ENDPOINTS } from "@/globals";
import { useMutation, useQuery, useQueryClient, useInfiniteQuery } from "@tanstack/react-query";
import { apiClient } from "./apiClient";
import { IDType } from "@/types";

export const useModels = ({ params = {} }) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_MODELS, {
        params,
      });
      return response;
    },
    queryKey: ["models", ...Object.values(params)],
  });

export const useInfiniteModels = ({ params = {} }) =>
  useInfiniteQuery({
    queryFn: async ({ pageParam = 1 }) => {
      const response = await apiClient.get(API_ENDPOINTS.GET_MODELS, {
        params: { ...params, page: pageParam },
      });
      return response;
    },
    queryKey: ["infinite-models", ...Object.values(params)],
    getNextPageParam: (lastPage) => {
      const { meta } = lastPage?.data || {};
      const currentPage = parseInt(meta?.page || "1");
      const totalPages = meta?.pages || 1;

      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    initialPageParam: 1,
  });

export const useFlirtMessages = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_FLIRT_MESSAGES);
      return response;
    },
    queryKey: ["flirt-messages"],
  });

export const useBotMessages = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_BOT_MESSAGES);
      return response;
    },
    queryKey: ["bot-messages"],
  });

export const useModelDetails = (model_id: IDType) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.GET_MODEL_DETAILS(model_id)
      );
      return response;
    },
    queryKey: ["model-details", model_id],
  });

export const useToggleFavorite = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (payload: Record<string, any>) => {
      return apiClient.post(API_ENDPOINTS.TOGGLE_FAVORITE, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["models"] });
    },
  });
};
